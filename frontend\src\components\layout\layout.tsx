import React, { useState, useEffect } from 'react';
import { Outlet, Link, useLocation, useNavigate } from 'react-router-dom';
import { Home, Calendar, Heart, Users, User, LogOut, Settings, Facebook, Twitter, Instagram, Mail, Bell } from 'lucide-react';
import clsx from 'clsx';
import { useAuth } from '../../contexts/AuthContext';
import { motion, AnimatePresence } from 'framer-motion';
import NotificationsPanel from '../../pages/NotificationsPanel';
import UserAvatar from '../common/UserAvatar';
import { useNotifications } from '../../hooks/useNotifications';


// Import styles
import '../../styles/globals.css';
import '../../styles/layout.css';

const menuItems = [
  { label: 'Trang chủ', to: '/', icon: <Home size={20} /> },
  { label: 'Sự kiện', to: '/events', icon: <Calendar size={20} /> },
  { label: '<PERSON>uy<PERSON><PERSON> góp', to: '/campaigns', icon: <Heart size={20} /> },
  { label: '<PERSON><PERSON><PERSON> viết', to: '/posts', icon: <Users size={20} /> },
];

const userMenuItems = [
  { label: 'Sự kiện của tôi', to: '/my-events', icon: <Calendar size={20} /> },
];

const Layout: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { user, logout } = useAuth();
  const [isProfileOpen, setIsProfileOpen] = useState(false);
  const [isNotificationsPanelOpen, setIsNotificationsPanelOpen] = useState(false);

  // Use real notifications hook
  const {
    notifications,
    unreadCount,
    hasNewNotifications,
    markAllAsRead,
    refetch
  } = useNotifications();

  const handleLogout = async () => {
    try {
      await logout();
      navigate('/login');
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  // Handle navigation with authentication check
  const handleNavigation = (to: string, event: React.MouseEvent) => {
    // Check if this is the posts route and user is not authenticated
    if (to === '/posts' && !user) {
      event.preventDefault();
      navigate('/login');
      return;
    }
    // For other routes, let normal navigation proceed
  };

  const handleBellClick = () => {
    setIsNotificationsPanelOpen(!isNotificationsPanelOpen);
    // Mark all notifications as read when panel is opened
    if (hasNewNotifications) {
      markAllAsRead();
    }
  };

  const handleCloseNotificationsPanel = () => {
    setIsNotificationsPanelOpen(false);
  };

  // Close profile dropdown when notification panel is opened
  useEffect(() => {
    if (isNotificationsPanelOpen) {
      setIsProfileOpen(false);
    }
  }, [isNotificationsPanelOpen]);

  // Close notification panel when profile dropdown is opened
  useEffect(() => {
    if (isProfileOpen) {
      setIsNotificationsPanelOpen(false);
    }
  }, [isProfileOpen]);

  // Effect to close panel/dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (isProfileOpen && !target.closest('.profile-dropdown')) {
        setIsProfileOpen(false);
      }
      // Assuming notification panel also has a way to be identified, e.g., a class name
      // if (isNotificationsPanelOpen && !target.closest('.notification-panel')) {
      //   setIsNotificationsPanelOpen(false);
      // }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isProfileOpen, isNotificationsPanelOpen]); // Include both states in dependency array

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-gradient-to-r from-menu-600/95 to-footer-500/95 shadow-xl fixed w-full top-0 z-50 backdrop-blur-md border-b border-white/20">
        <div className="absolute inset-0 bg-gradient-to-r from-purple-600/10 to-pink-600/10"></div>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-20">
            {/* Logo */}
            <Link to="/" className="flex items-center group">
              <motion.img
                whileHover={{ scale: 1.05, rotate: 5 }}
                src="/logo.png"
                alt="Logo"
                className="h-10 w-auto transform transition-transform duration-300"
              />
              <motion.span
                whileHover={{ x: 5 }}
                className="ml-3 text-2xl font-bold text-white group-hover:text-gray-100 transition-colors duration-300"
              >
                KeyDyWeb
              </motion.span>
            </Link>

            {/* Navigation */}
            <nav className="hidden md:flex space-x-6">
              {menuItems.map((item) => (
                <Link
                  key={item.to}
                  to={item.to}
                  onClick={(e) => handleNavigation(item.to, e)}
                  className={clsx(
                    'nav-link flex items-center px-5 py-2.5 text-sm font-medium rounded-xl transition-all duration-300',
                    location.pathname === item.to
                      ? 'text-white bg-white/20 shadow-lg backdrop-blur-sm border border-white/20'
                      : 'text-gray-100 hover:text-white hover:bg-white/10 hover:shadow-md backdrop-blur-sm border border-transparent hover:border-white/20'
                  )}
                >
                  <motion.div
                    whileHover={{ scale: 1.1 }}
                    className="mr-2"
                  >
                    {item.icon}
                  </motion.div>
                  <span>{item.label}</span>
                </Link>
              ))}
            </nav>

            {/* Right side buttons */}
            <div className="flex items-center space-x-4 overflow-visible relative">
              {user ? (
                <>
                  {/* Notification Bell */}
                  <div className="relative">
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className={clsx(
                        "flex items-center text-white hover:text-gray-100 bg-white/10 hover:bg-white/20 p-2 rounded-xl transition-all duration-300 backdrop-blur-sm border border-white/10 hover:border-white/20",
                        hasNewNotifications && 'shake-animation'
                      )}
                      aria-label="Notifications"
                      onClick={handleBellClick}
                    >
                      <Bell size={20} />
                    </motion.button>
                    {hasNewNotifications && (
                      <span className="absolute top-0 right-0 w-3 h-3 bg-red-500 rounded-full animate-pulse"></span>
                    )}
                  </div>

                  {/* User Profile Button */}
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => setIsProfileOpen(!isProfileOpen)}
                    className="flex items-center space-x-2 text-white hover:text-gray-100 bg-white/10 hover:bg-white/20 px-3 py-2.5 rounded-xl transition-all duration-300 backdrop-blur-sm border border-white/10 hover:border-white/20"
                  >
                    {/* User Avatar */}
                    <UserAvatar user={user} size="sm" className="border border-white/20" />
                    <span className="hidden md:inline">{user.name}</span>
                  </motion.button>

                  {/* Profile Dropdown Menu */}
                  <AnimatePresence>
                    {isProfileOpen && (
                      <motion.div
                        initial={{ opacity: 0, y: -8 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -8 }}
                        transition={{ duration: 0.2 }}
                        className="absolute top-full right-0 mt-2 w-56 bg-white/95 backdrop-blur-md rounded-xl shadow-xl py-2 border border-white/20 z-50 origin-top-right profile-dropdown"
                      >
                        {userMenuItems.map((item) => (
                          <Link
                            key={item.to}
                            to={item.to}
                            className="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-white/50 hover:text-primary transition-colors duration-200"
                          >
                            {React.cloneElement(item.icon, { size: 16, className: "mr-3" })}
                            <span>{item.label}</span>
                          </Link>
                        ))}
                        <div className="border-t border-gray-100 my-1" />
                        <Link
                          to="/profile"
                          className="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-white/50 hover:text-primary transition-colors duration-200"
                        >
                          <User size={16} className="mr-3" />
                          <span>Tài khoản</span>
                        </Link>
                        <Link
                          to="/settings"
                          className="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-white/50 hover:text-primary transition-colors duration-200"
                        >
                          <Settings size={16} className="mr-3" />
                          <span>Cài đặt</span>
                        </Link>
                        {user && user.role === 'admin' && (
                          <Link
                            to="/admin"
                            className="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-white/50 hover:text-primary transition-colors duration-200"
                          >
                            <Settings size={16} className="mr-3" />
                            <span>Quản trị viên</span>
                          </Link>
                        )}
                        <div className="border-t border-gray-100 my-1" />
                        <button
                          onClick={handleLogout}
                          className="flex items-center w-full text-left px-4 py-3 text-sm text-gray-700 hover:bg-white/50 hover:text-red-500 transition-colors duration-200"
                        >
                          <LogOut size={16} className="mr-3" />
                          <span>Đăng xuất</span>
                        </button>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </>
              ) : (
                <>
                  <Link
                    to="/login"
                    className="bg-white text-menu-600 hover:bg-gray-100 px-8 py-2.5 rounded-xl font-medium transition-all duration-300 hover:shadow-lg hover:shadow-white/20 flex items-center space-x-2 border border-transparent hover:border-white/20"
                  >
                    <User size={18} />
                    <span>Đăng nhập</span>
                  </Link>
                </>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Main content */}
      <main className="pt-20 min-h-screen bg-gradient-to-b from-gray-50 to-gray-100">
        <Outlet />
      </main>

      {/* Notifications Panel */}
      <NotificationsPanel
        notifications={notifications}
        isOpen={isNotificationsPanelOpen}
        onClose={handleCloseNotificationsPanel}
      />



      {/* Footer */}
      <footer className="bg-gradient-to-b from-gray-800 via-gray-850 to-gray-900 text-white border-t border-gray-700/50 relative overflow-hidden">
        {/* Background decoration */}
        <div className="absolute inset-0 bg-gradient-to-r from-purple-900/10 to-pink-900/10"></div>
        <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-purple-500/30 to-transparent"></div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 relative z-10">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-12">
            <div>
              <h3 className="text-xl font-bold mb-6 text-white">Về KeyDyWeb</h3>
              <p className="text-gray-200 leading-relaxed">
                Nền tảng kết nối cộng đồng, chia sẻ yêu thương và lan tỏa những giá trị nhân văn.
              </p>
              <p className="text-gray-200 text-sm italic mt-4">
                Lan tỏa yêu thương, đồng tâm giúp đỡ
              </p>
            </div>
            <div>
              <h3 className="text-xl font-bold mb-6 text-white">Liên kết</h3>
              <ul className="space-y-4">
                <li>
                  <Link to="/about" className="footer-link text-gray-200 hover:text-white transition-colors duration-200 flex items-center">
                    <span className="w-1.5 h-1.5 bg-white/50 rounded-full mr-3"></span>
                    Giới thiệu
                  </Link>
                </li>
                <li>
                  <Link to="/contact" className="footer-link text-gray-200 hover:text-white transition-colors duration-200 flex items-center">
                    <span className="w-1.5 h-1.5 bg-white/50 rounded-full mr-3"></span>
                    Liên hệ
                  </Link>
                </li>
                <li>
                  <Link to="/faq" className="footer-link text-gray-200 hover:text-white transition-colors duration-200 flex items-center">
                    <span className="w-1.5 h-1.5 bg-white/50 rounded-full mr-3"></span>
                    FAQ
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h3 className="text-xl font-bold mb-6 text-white">Theo dõi</h3>
              <div className="flex space-x-6">
                <motion.a
                  whileHover={{ scale: 1.1, y: -2 }}
                  whileTap={{ scale: 0.9 }}
                  href="#"
                  className="footer-link text-gray-200 hover:text-white transition-colors duration-200 bg-white/10 p-3 rounded-xl hover:bg-white/20"
                >
                  <Facebook size={20} />
                </motion.a>
                <motion.a
                  whileHover={{ scale: 1.1, y: -2 }}
                  whileTap={{ scale: 0.9 }}
                  href="#"
                  className="footer-link text-gray-200 hover:text-white transition-colors duration-200 bg-white/10 p-3 rounded-xl hover:bg-white/20"
                >
                  <Twitter size={20} />
                </motion.a>
                <motion.a
                  whileHover={{ scale: 1.1, y: -2 }}
                  whileTap={{ scale: 0.9 }}
                  href="#"
                  className="footer-link text-gray-200 hover:text-white transition-colors duration-200 bg-white/10 p-3 rounded-xl hover:bg-white/20"
                >
                  <Instagram size={20} />
                </motion.a>
              </div>
            </div>
            <div>
              <h3 className="text-xl font-bold mb-6 text-white">Liên hệ</h3>
              <ul className="space-y-4">
                <li className="flex items-center text-gray-200 hover:text-white transition-colors duration-200">
                  <div className="bg-white/10 p-2 rounded-lg mr-3">
                    <Mail size={16} />
                  </div>
                  <span><EMAIL></span>
                </li>
                <li className="flex items-center text-gray-200 hover:text-white transition-colors duration-200">
                  <div className="bg-white/10 p-2 rounded-lg mr-3">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                    </svg>
                  </div>
                  <span>0977 831 549</span>
                </li>
                <li className="flex items-center text-gray-200 hover:text-white transition-colors duration-200">
                  <div className="bg-white/10 p-2 rounded-lg mr-3">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                  </div>
                  <span>Khóm 4, Phường 5, TP.TV, tỉnh Trà Vinh</span>
                </li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-700 mt-12 pt-8 text-center text-gray-300">
            <p>&copy; {new Date().getFullYear()} KeyDyWeb. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Layout;