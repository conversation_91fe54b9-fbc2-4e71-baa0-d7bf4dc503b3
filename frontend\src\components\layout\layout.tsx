import React, { useState, useEffect } from 'react';
import { Outlet, Link, useLocation, useNavigate } from 'react-router-dom';
import { Home, Calendar, Heart, Users, User, LogOut, Settings, Facebook, Twitter, Instagram, Mail, Bell, Phone, MapPin } from 'lucide-react';
import clsx from 'clsx';
import { useAuth } from '../../contexts/AuthContext';
import { motion, AnimatePresence } from 'framer-motion';
import NotificationsPanel from '../../pages/NotificationsPanel';
import UserAvatar from '../common/UserAvatar';
import { useNotifications } from '../../hooks/useNotifications';


// Import styles
import '../../styles/globals.css';
import '../../styles/layout.css';

const menuItems = [
  { label: 'Trang chủ', to: '/', icon: <Home size={20} /> },
  { label: 'Sự kiện', to: '/events', icon: <Calendar size={20} /> },
  { label: '<PERSON><PERSON><PERSON><PERSON> góp', to: '/campaigns', icon: <Heart size={20} /> },
  { label: 'Bài viết', to: '/posts', icon: <Users size={20} /> },
];

const userMenuItems = [
  { label: 'Sự kiện của tôi', to: '/my-events', icon: <Calendar size={20} /> },
];

const Layout: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { user, logout } = useAuth();
  const [isProfileOpen, setIsProfileOpen] = useState(false);
  const [isNotificationsPanelOpen, setIsNotificationsPanelOpen] = useState(false);

  // Use real notifications hook
  const {
    notifications,
    unreadCount,
    hasNewNotifications,
    markAllAsRead,
    refetch
  } = useNotifications();

  const handleLogout = async () => {
    try {
      await logout();
      navigate('/login');
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  // Handle navigation with authentication check
  const handleNavigation = (to: string, event: React.MouseEvent) => {
    // Check if this is the posts route and user is not authenticated
    if (to === '/posts' && !user) {
      event.preventDefault();
      navigate('/login');
      return;
    }
    // For other routes, let normal navigation proceed
  };

  const handleBellClick = () => {
    setIsNotificationsPanelOpen(!isNotificationsPanelOpen);
    // Mark all notifications as read when panel is opened
    if (hasNewNotifications) {
      markAllAsRead();
    }
  };

  const handleCloseNotificationsPanel = () => {
    setIsNotificationsPanelOpen(false);
  };

  // Close profile dropdown when notification panel is opened
  useEffect(() => {
    if (isNotificationsPanelOpen) {
      setIsProfileOpen(false);
    }
  }, [isNotificationsPanelOpen]);

  // Close notification panel when profile dropdown is opened
  useEffect(() => {
    if (isProfileOpen) {
      setIsNotificationsPanelOpen(false);
    }
  }, [isProfileOpen]);

  // Effect to close panel/dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (isProfileOpen && !target.closest('.profile-dropdown')) {
        setIsProfileOpen(false);
      }
      // Assuming notification panel also has a way to be identified, e.g., a class name
      // if (isNotificationsPanelOpen && !target.closest('.notification-panel')) {
      //   setIsNotificationsPanelOpen(false);
      // }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isProfileOpen, isNotificationsPanelOpen]); // Include both states in dependency array

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-gradient-to-r from-menu-600/95 to-footer-500/95 shadow-xl fixed w-full top-0 z-50 backdrop-blur-md border-b border-white/20">
        <div className="absolute inset-0 bg-gradient-to-r from-purple-600/10 to-pink-600/10"></div>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-20">
            {/* Logo */}
            <Link to="/" className="flex items-center group">
              <motion.img
                whileHover={{ scale: 1.05, rotate: 5 }}
                src="/logo.png"
                alt="Logo"
                className="h-10 w-auto transform transition-transform duration-300"
              />
              <motion.span
                whileHover={{ x: 5 }}
                className="ml-3 text-2xl font-bold text-white group-hover:text-gray-100 transition-colors duration-300"
              >
                KeyDyWeb
              </motion.span>
            </Link>

            {/* Navigation */}
            <nav className="hidden md:flex space-x-6">
              {menuItems.map((item) => (
                <Link
                  key={item.to}
                  to={item.to}
                  onClick={(e) => handleNavigation(item.to, e)}
                  className={clsx(
                    'nav-link flex items-center px-5 py-2.5 text-sm font-medium rounded-xl transition-all duration-300',
                    location.pathname === item.to
                      ? 'text-white bg-white/20 shadow-lg backdrop-blur-sm border border-white/20'
                      : 'text-gray-100 hover:text-white hover:bg-white/10 hover:shadow-md backdrop-blur-sm border border-transparent hover:border-white/20'
                  )}
                >
                  <motion.div
                    whileHover={{ scale: 1.1 }}
                    className="mr-2"
                  >
                    {item.icon}
                  </motion.div>
                  <span>{item.label}</span>
                </Link>
              ))}
            </nav>

            {/* Right side buttons */}
            <div className="flex items-center space-x-4 overflow-visible relative">
              {user ? (
                <>
                  {/* Notification Bell */}
                  <div className="relative">
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className={clsx(
                        "flex items-center text-white hover:text-gray-100 bg-white/10 hover:bg-white/20 p-2 rounded-xl transition-all duration-300 backdrop-blur-sm border border-white/10 hover:border-white/20",
                        hasNewNotifications && 'shake-animation'
                      )}
                      aria-label="Notifications"
                      onClick={handleBellClick}
                    >
                      <Bell size={20} />
                    </motion.button>
                    {hasNewNotifications && (
                      <span className="absolute top-0 right-0 w-3 h-3 bg-red-500 rounded-full animate-pulse"></span>
                    )}
                  </div>

                  {/* User Profile Button */}
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => setIsProfileOpen(!isProfileOpen)}
                    className="flex items-center space-x-2 text-white hover:text-gray-100 bg-white/10 hover:bg-white/20 px-3 py-2.5 rounded-xl transition-all duration-300 backdrop-blur-sm border border-white/10 hover:border-white/20"
                  >
                    {/* User Avatar */}
                    <UserAvatar user={user} size="sm" className="border border-white/20" />
                    <span className="hidden md:inline">{user.name}</span>
                  </motion.button>

                  {/* Profile Dropdown Menu */}
                  <AnimatePresence>
                    {isProfileOpen && (
                      <motion.div
                        initial={{ opacity: 0, y: -8 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -8 }}
                        transition={{ duration: 0.2 }}
                        className="absolute top-full right-0 mt-2 w-56 bg-white/95 backdrop-blur-md rounded-xl shadow-xl py-2 border border-white/20 z-50 origin-top-right profile-dropdown"
                      >
                        {userMenuItems.map((item) => (
                          <Link
                            key={item.to}
                            to={item.to}
                            className="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-white/50 hover:text-primary transition-colors duration-200"
                          >
                            {React.cloneElement(item.icon, { size: 16, className: "mr-3" })}
                            <span>{item.label}</span>
                          </Link>
                        ))}
                        <div className="border-t border-gray-100 my-1" />
                        <Link
                          to="/profile"
                          className="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-white/50 hover:text-primary transition-colors duration-200"
                        >
                          <User size={16} className="mr-3" />
                          <span>Tài khoản</span>
                        </Link>
                        <Link
                          to="/settings"
                          className="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-white/50 hover:text-primary transition-colors duration-200"
                        >
                          <Settings size={16} className="mr-3" />
                          <span>Cài đặt</span>
                        </Link>
                        {user && user.role === 'admin' && (
                          <Link
                            to="/admin"
                            className="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-white/50 hover:text-primary transition-colors duration-200"
                          >
                            <Settings size={16} className="mr-3" />
                            <span>Quản trị viên</span>
                          </Link>
                        )}
                        <div className="border-t border-gray-100 my-1" />
                        <button
                          onClick={handleLogout}
                          className="flex items-center w-full text-left px-4 py-3 text-sm text-gray-700 hover:bg-white/50 hover:text-red-500 transition-colors duration-200"
                        >
                          <LogOut size={16} className="mr-3" />
                          <span>Đăng xuất</span>
                        </button>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </>
              ) : (
                <>
                  <Link
                    to="/login"
                    className="bg-white text-menu-600 hover:bg-gray-100 px-8 py-2.5 rounded-xl font-medium transition-all duration-300 hover:shadow-lg hover:shadow-white/20 flex items-center space-x-2 border border-transparent hover:border-white/20"
                  >
                    <User size={18} />
                    <span>Đăng nhập</span>
                  </Link>
                </>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Main content */}
      <main className="pt-20 min-h-screen bg-gradient-to-b from-gray-50 to-gray-100">
        <Outlet />
      </main>

      {/* Notifications Panel */}
      <NotificationsPanel
        notifications={notifications}
        isOpen={isNotificationsPanelOpen}
        onClose={handleCloseNotificationsPanel}
      />



      {/* Footer */}
      <footer className="relative bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white overflow-hidden">
        {/* Background decorations */}
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-gradient-to-r from-indigo-900/20 via-purple-900/20 to-pink-900/20"></div>
          <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-indigo-400/50 to-transparent"></div>

          {/* Decorative elements */}
          <div className="absolute top-10 left-10 w-32 h-32 bg-gradient-to-br from-indigo-500/10 to-purple-500/10 rounded-full blur-xl"></div>
          <div className="absolute bottom-10 right-10 w-40 h-40 bg-gradient-to-br from-purple-500/10 to-pink-500/10 rounded-full blur-xl"></div>
          <div className="absolute top-1/2 left-1/3 w-24 h-24 bg-gradient-to-br from-blue-500/10 to-indigo-500/10 rounded-full blur-lg"></div>
        </div>

        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Main footer content */}
          <div className="py-16">
            <div className="grid grid-cols-1 lg:grid-cols-12 gap-12">

              {/* Brand section */}
              <div className="lg:col-span-4">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6 }}
                >
                  <div className="flex items-center mb-6">
                    <div className="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center mr-4 shadow-lg">
                      <Heart className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="text-2xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                      KeyDyWeb
                    </h3>
                  </div>
                  <p className="text-gray-300 leading-relaxed mb-6 text-lg">
                    Nền tảng kết nối cộng đồng hàng đầu, lan tỏa yêu thương và tạo nên những thay đổi tích cực cho xã hội Việt Nam.
                  </p>
                  <div className="bg-gradient-to-r from-indigo-500/20 to-purple-500/20 backdrop-blur-sm rounded-xl p-4 border border-white/10">
                    <p className="text-indigo-200 font-medium italic">
                      "Lan tỏa yêu thương, đồng tâm giúp đỡ"
                    </p>
                  </div>
                </motion.div>
              </div>

              {/* Quick links */}
              <div className="lg:col-span-2">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.1 }}
                >
                  <h4 className="text-lg font-semibold mb-6 text-white">Khám phá</h4>
                  <ul className="space-y-3">
                    {[
                      { name: 'Sự kiện', path: '/events' },
                      { name: 'Chiến dịch', path: '/campaigns' },
                      { name: 'Về chúng tôi', path: '/about' },
                      { name: 'Liên hệ', path: '/contact' }
                    ].map((item, index) => (
                      <li key={index}>
                        <Link
                          to={item.path}
                          className="group flex items-center text-gray-300 hover:text-white transition-all duration-300"
                        >
                          <div className="w-1.5 h-1.5 bg-indigo-400 rounded-full mr-3 group-hover:bg-white transition-colors duration-300"></div>
                          <span className="group-hover:translate-x-1 transition-transform duration-300">{item.name}</span>
                        </Link>
                      </li>
                    ))}
                  </ul>
                </motion.div>
              </div>

              {/* Support links */}
              <div className="lg:col-span-2">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                >
                  <h4 className="text-lg font-semibold mb-6 text-white">Hỗ trợ</h4>
                  <ul className="space-y-3">
                    {[
                      { name: 'Câu hỏi thường gặp', path: '/faq' },
                      { name: 'Hướng dẫn sử dụng', path: '/guide' },
                      { name: 'Chính sách bảo mật', path: '/privacy' },
                      { name: 'Điều khoản sử dụng', path: '/terms' }
                    ].map((item, index) => (
                      <li key={index}>
                        <Link
                          to={item.path}
                          className="group flex items-center text-gray-300 hover:text-white transition-all duration-300"
                        >
                          <div className="w-1.5 h-1.5 bg-purple-400 rounded-full mr-3 group-hover:bg-white transition-colors duration-300"></div>
                          <span className="group-hover:translate-x-1 transition-transform duration-300">{item.name}</span>
                        </Link>
                      </li>
                    ))}
                  </ul>
                </motion.div>
              </div>

              {/* Contact info */}
              <div className="lg:col-span-4">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.3 }}
                >
                  <h4 className="text-lg font-semibold mb-6 text-white">Liên hệ với chúng tôi</h4>
                  <div className="space-y-4 mb-8">
                    <div className="flex items-center group">
                      <div className="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300">
                        <Mail size={18} className="text-white" />
                      </div>
                      <div>
                        <p className="text-gray-400 text-sm">Email</p>
                        <p className="text-white font-medium"><EMAIL></p>
                      </div>
                    </div>
                    <div className="flex items-center group">
                      <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300">
                        <Phone size={18} className="text-white" />
                      </div>
                      <div>
                        <p className="text-gray-400 text-sm">Hotline</p>
                        <p className="text-white font-medium">0977 831 549</p>
                      </div>
                    </div>
                    <div className="flex items-start group">
                      <div className="w-10 h-10 bg-gradient-to-br from-pink-500 to-red-600 rounded-lg flex items-center justify-center mr-4 mt-1 group-hover:scale-110 transition-transform duration-300">
                        <MapPin size={18} className="text-white" />
                      </div>
                      <div>
                        <p className="text-gray-400 text-sm">Địa chỉ</p>
                        <p className="text-white font-medium">Khóm 4, Phường 5, TP.TV, tỉnh Trà Vinh</p>
                      </div>
                    </div>
                  </div>

                  {/* Social media */}
                  <div>
                    <h5 className="text-white font-medium mb-4">Theo dõi chúng tôi</h5>
                    <div className="flex space-x-4">
                      {[
                        { icon: Facebook, color: 'from-blue-500 to-blue-600', href: '#' },
                        { icon: Twitter, color: 'from-sky-500 to-sky-600', href: '#' },
                        { icon: Instagram, color: 'from-pink-500 to-purple-600', href: '#' }
                      ].map((social, index) => (
                        <motion.a
                          key={index}
                          href={social.href}
                          whileHover={{ scale: 1.1, y: -2 }}
                          whileTap={{ scale: 0.95 }}
                          className={`w-12 h-12 bg-gradient-to-br ${social.color} rounded-xl flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-300`}
                        >
                          <social.icon size={20} className="text-white" />
                        </motion.a>
                      ))}
                    </div>
                  </div>
                </motion.div>
              </div>
            </div>
          </div>

          {/* Bottom section */}
          <div className="border-t border-white/10 py-8">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                className="text-gray-400 text-sm mb-4 md:mb-0"
              >
                <p>&copy; {new Date().getFullYear()} KeyDyWeb. Tất cả quyền được bảo lưu.</p>
              </motion.div>
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.6, delay: 0.5 }}
                className="flex items-center space-x-6 text-sm text-gray-400"
              >
                <Link to="/privacy" className="hover:text-white transition-colors duration-300">
                  Chính sách bảo mật
                </Link>
                <span className="w-1 h-1 bg-gray-500 rounded-full"></span>
                <Link to="/terms" className="hover:text-white transition-colors duration-300">
                  Điều khoản sử dụng
                </Link>
                <span className="w-1 h-1 bg-gray-500 rounded-full"></span>
                <Link to="/sitemap" className="hover:text-white transition-colors duration-300">
                  Sơ đồ trang web
                </Link>
              </motion.div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Layout;